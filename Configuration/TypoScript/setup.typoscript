
# Include SEO extension TypoScript (required for XML sitemap functionality)
@import 'EXT:seo/Configuration/TypoScript/XmlSitemap/setup.typoscript'

# XML Sitemap configuration for Flight Routes
# Add our custom flight routes sitemap to the existing configuration
plugin.tx_seo.config.xmlSitemap.sitemaps.landingPages {
    provider = Bgs\LandingPages\XmlSitemap\FlightRoutesXmlSitemapDataProvider
    config {
        # Configuration can be added here if needed in the future
    }
}

# Landing Pages Extension Setup

# Note: fluid_styled_content import removed to prevent interference with existing sites
# Sites should include fluid_styled_content in their own TypoScript templates


# Configuration for Template Pages (doktype 200)
# These pages render normally but are typically not linked in navigation
# They serve as content templates for Landing Pages

# Landing Pages use the site's existing PAGE object configuration
# No custom PAGE object needed - they render as normal TYPO3 pages

# Note: The VirtualRouteDataProcessor is now only added to specific page types
# via the FlightRouteProcessor, not globally to all pages.
# This prevents interference with existing site TypoScript configurations.

# Note: lib.dynamicContent override removed to prevent interference with existing sites
# Virtual route content rendering is now handled by specific data processors
# and event listeners, not by global TypoScript overrides.



# Plugin configuration for RouteReference list (when used as content element)
plugin.tx_landingpages {
    view {
        templateRootPaths {
            0 = EXT:landing-pages/Resources/Private/Templates/
            1 = {$plugin.tx_landingpages.view.templateRootPath}
        }
        partialRootPaths {
            0 = EXT:fluid_styled_content/Resources/Private/Partials/
            1 = {$plugin.tx_landingpages.view.partialRootPath}
        }
        layoutRootPaths {
            0 = EXT:landing-pages/Resources/Private/Layouts/
            1 = {$plugin.tx_landingpages.view.layoutRootPath}
        }
    }

    persistence {
        storagePid = {$plugin.tx_landingpages.persistence.storagePid}
    }

    settings {
        # API configuration for flight data
        api {
            baseUrl = https://api.example.com/flights
            apiKey =
            cacheLifetime = 3600
        }
    }
}

# Fallback configuration for any remaining plugin references
# This prevents errors if there are still old plugin content elements in the database
tt_content.list.20.landingpages_destinationsmenu = USER
tt_content.list.20.landingpages_destinationsmenu {
    userFunc = TYPO3\CMS\Extbase\Core\Bootstrap->run
    extensionName = LandingPages
    pluginName = DestinationsMenu
    vendorName = Bgs
}

# Content element configuration for Destination Pairs Menu
tt_content.landingpages_destinationpairsmenu = FLUIDTEMPLATE
tt_content.landingpages_destinationpairsmenu {
    templateName = DestinationPairsMenu
    templateRootPaths {
        0 = EXT:landing-pages/Resources/Private/Templates/ContentElements/
        10 = {$plugin.tx_landingpages.view.templateRootPath}ContentElements/
    }
    partialRootPaths {
        0 = EXT:fluid_styled_content/Resources/Private/Partials/
        10 = {$plugin.tx_landingpages.view.partialRootPath}
    }
    layoutRootPaths {
        0 = EXT:fluid_styled_content/Resources/Private/Layouts/
        10 = {$plugin.tx_landingpages.view.layoutRootPath}
    }
    dataProcessing {
        10 = Bgs\LandingPages\DataProcessing\DestinationPairsMenuProcessor
    }
}

# Include CSS and JS only for landing pages
[page["doktype"] == 201]
    page {
        includeCSS {
            landingpages = EXT:landing-pages/Resources/Public/CSS/styles.css
        }
        includeJSFooter {
            landingpages = EXT:landing-pages/Resources/Public/JavaScript/main.js
        }
    }
[END]
