
# Landing Pages Extension - Safe Setup
# This file contains only essential configurations without global modifications
# All configurations are conditional to prevent interference with existing sites

# XML Sitemap configuration for Flight Routes
# Simple configuration without complex conditionals to avoid errors
plugin.tx_seo.config.xmlSitemap.sitemaps.landingPages {
    provider = Bgs\LandingPages\XmlSitemap\FlightRoutesXmlSitemapDataProvider
    config {
        # Configuration can be added here if needed in the future
    }
}

# Plugin configuration for RouteReference list (when used as content element)
plugin.tx_landingpages {
    view {
        templateRootPaths {
            0 = EXT:landing-pages/Resources/Private/Templates/
            1 = {$plugin.tx_landingpages.view.templateRootPath}
        }
        partialRootPaths {
            0 = EXT:fluid_styled_content/Resources/Private/Partials/
            1 = {$plugin.tx_landingpages.view.partialRootPath}
        }
        layoutRootPaths {
            0 = EXT:landing-pages/Resources/Private/Layouts/
            1 = {$plugin.tx_landingpages.view.layoutRootPath}
        }
    }

    persistence {
        storagePid = {$plugin.tx_landingpages.persistence.storagePid}
    }

    settings {
        # API configuration for flight data
        api {
            baseUrl = https://api.example.com/flights
            apiKey =
            cacheLifetime = 3600
        }
    }
}

# Fallback configuration for any remaining plugin references
# This prevents errors if there are still old plugin content elements in the database
tt_content.list.20.landingpages_destinationsmenu = USER
tt_content.list.20.landingpages_destinationsmenu {
    userFunc = TYPO3\CMS\Extbase\Core\Bootstrap->run
    extensionName = LandingPages
    pluginName = DestinationsMenu
    vendorName = Bgs
}

# Content element configuration for Destination Pairs Menu
# Only load on Landing Pages (doktype 201) to prevent interference with other pages
[page["doktype"] == 201]
    tt_content.landingpages_destinationpairsmenu = FLUIDTEMPLATE
    tt_content.landingpages_destinationpairsmenu {
        templateName = DestinationPairsMenu
        templateRootPaths {
            0 = EXT:landing-pages/Resources/Private/Templates/ContentElements/
            10 = {$plugin.tx_landingpages.view.templateRootPath}ContentElements/
        }
        partialRootPaths {
            0 = EXT:fluid_styled_content/Resources/Private/Partials/
            10 = {$plugin.tx_landingpages.view.partialRootPath}
        }
        layoutRootPaths {
            0 = EXT:fluid_styled_content/Resources/Private/Layouts/
            10 = {$plugin.tx_landingpages.view.layoutRootPath}
        }
        dataProcessing {
            10 = Bgs\LandingPages\DataProcessing\DestinationPairsMenuProcessor
        }
    }
[END]

# Include CSS and JS only for landing pages
[page["doktype"] == 201]
    page {
        includeCSS {
            landingpages = EXT:landing-pages/Resources/Public/CSS/styles.css
        }
        includeJSFooter {
            landingpages = EXT:landing-pages/Resources/Public/JavaScript/main.js
        }
    }
[END]
