<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Middleware;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Log\LogManager;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * This middleware adds dynamic arguments to the routing for virtual routes only
 * It runs after PageArgumentValidator but before TypoScriptFrontendInitialization
 * This allows us to manipulate the dynamic arguments and cHash after routing has been processed
 *
 * Note: This middleware only processes requests that have a valid VirtualRouteContext
 */

class DynamicArgumentsMiddleware implements MiddlewareInterface
{
    /**
     * Process the request by adding dynamic arguments to the routing
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $logger = GeneralUtility::makeInstance(LogManager::class)->getLogger(__CLASS__);
        /** @var PageArguments|null $pageArguments */
        $pageArguments = $request->getAttribute('routing');

        if ($pageArguments instanceof PageArguments) {
            // Get the current arguments
            $arguments = $pageArguments->getArguments();

            $virtualRouteContext = $request->getAttribute('landing-pages.virtual_route_context');

            // Log for debugging
            $logger->debug('DynamicArgumentsMiddleware: Processing request', [
                'uri' => (string)$request->getUri(),
                'hasVirtualRouteContext' => $virtualRouteContext !== null,
                'contextType' => $virtualRouteContext ? get_class($virtualRouteContext) : 'null'
            ]);

            // Only process if we have a virtual route context and it's a VirtualRouteContext instance
            if (!$virtualRouteContext instanceof VirtualRouteContext || !$virtualRouteContext->isVirtualRoute()) {
                $logger->debug('DynamicArgumentsMiddleware: No valid virtual route context, skipping');
                return $handler->handle($request);
            }

            $originalPath = $virtualRouteContext->getOriginalPath();
            if ($originalPath === null) {
                $logger->warning('DynamicArgumentsMiddleware: Virtual route context has null original path');
                return $handler->handle($request);
            }

            $arguments['lp_route'] = $originalPath;
            $arguments['cHash'] = sha1($originalPath);

            $newPageArguments = GeneralUtility::makeInstance(
                PageArguments::class,
                $pageArguments->getPageId(),
                $pageArguments->getPageType(),
                $pageArguments->getRouteArguments(),
                $pageArguments->getStaticArguments(),
                array_merge($pageArguments->getDynamicArguments(), $arguments)
            );

            $request = $request->withAttribute('routing', $newPageArguments);
            return $handler->handle($request);
        }

        return $handler->handle($request);
    }
}
