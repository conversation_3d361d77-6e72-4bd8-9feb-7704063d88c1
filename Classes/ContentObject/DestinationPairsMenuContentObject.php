<?php

declare(strict_types=1);

namespace Bgs\LandingPages\ContentObject;

use Bgs\LandingPages\DataProcessing\DestinationPairsMenuProcessor;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Fluid\View\StandaloneView;

/**
 * UserFunc for rendering destination pairs menu content element
 *
 * This class handles the rendering of the destination pairs menu content element
 * using minimal TypoScript to prevent interference with existing site TypoScript.
 */
class DestinationPairsMenuContentObject
{
    /**
     * Render the destination pairs menu content element
     *
     * @param string $content Content (not used)
     * @param array $conf TypoScript configuration (not used)
     * @param ContentObjectRenderer $cObj Content object renderer
     * @return string Rendered content
     */
    public function render(string $content, array $conf, ContentObjectRenderer $cObj): string
    {
        try {
            // Get content element data
            $data = $cObj->data;

            // Process data using the destination pairs menu processor
            $processor = GeneralUtility::makeInstance(DestinationPairsMenuProcessor::class);
            $processedData = $processor->process(
                $cObj,
                [],
                [],
                ['data' => $data]
            );
            
            // Create Fluid view
            $view = GeneralUtility::makeInstance(StandaloneView::class);
            
            // Set template paths
            $view->setTemplateRootPaths([
                'EXT:landing-pages/Resources/Private/Templates/ContentElements/'
            ]);
            $view->setPartialRootPaths([
                'EXT:fluid_styled_content/Resources/Private/Partials/',
                'EXT:landing-pages/Resources/Private/Partials/'
            ]);
            $view->setLayoutRootPaths([
                'EXT:fluid_styled_content/Resources/Private/Layouts/',
                'EXT:landing-pages/Resources/Private/Layouts/'
            ]);
            
            // Set template
            $view->setTemplate('DestinationPairsMenu');
            
            // Assign variables to view
            $view->assignMultiple($processedData);
            
            // Render and return
            return $view->render();
            
        } catch (\Exception $e) {
            // Graceful error handling - return a safe error message
            return '<div class="alert alert-warning">Destination pairs menu temporarily unavailable.</div>';
        }
    }
}
