{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "a29041f01d8a323032bee202d0ffe7f7", "packages": [{"name": "bacon/bacon-qr-code", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "f9cc1f52b5a463062251d666761178dbdb6b544f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/f9cc1f52b5a463062251d666761178dbdb6b544f", "reference": "f9cc1f52b5a463062251d666761178dbdb6b544f", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^8.1"}, "require-dev": {"phly/keep-a-changelog": "^2.12", "phpunit/phpunit": "^10.5.11 || 11.0.4", "spatie/phpunit-snapshot-assertions": "^5.1.5", "squizlabs/php_codesniffer": "^3.9"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/v3.0.1"}, "time": "2024-10-01T13:55:55+00:00"}, {"name": "bgs/landing-pages", "version": "dev-main", "dist": {"type": "path", "url": "packages/landing-pages", "reference": "d46acbc6924db5e0abf0ea3ac742fa3384f9f428"}, "require": {"typo3/cms-core": "^12.4", "typo3/cms-extbase": "^12.4", "typo3/cms-fluid": "^12.4"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "landing-pages"}}, "autoload": {"psr-4": {"Bgs\\LandingPages\\": "Classes/"}}, "scripts": {"package": ["grep -Po \"(?<='version' => ')([0-9]+\\.[0-9]+\\.[0-9]+)\" ext_emconf.php | xargs -I {version} sh -c 'mkdir -p ../../zip; git archive -v -o \"../../zip/landing-pages_{version}.zip\" HEAD'"], "package:ter": ["@package"]}, "license": ["GPL-2.0-or-later"], "description": "TYPO3 extension for managing landing pages with dynamic content", "keywords": ["TYPO3", "extension", "landing pages", "virtual routes"], "transport-options": {"relative": true}}, {"name": "christian-riesen/base32", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/ChristianRiesen/base32.git", "reference": "2e82dab3baa008e24a505649b0d583c31d31e894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ChristianRiesen/base32/zipball/2e82dab3baa008e24a505649b0d583c31d31e894", "reference": "2e82dab3baa008e24a505649b0d583c31d31e894", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.17", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.5.13 || ^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Base32\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://christianriesen.com", "role": "Developer"}], "description": "Base32 encoder/decoder according to RFC 4648", "homepage": "https://github.com/ChristianRiesen/base32", "keywords": ["base32", "decode", "encode", "rfc4648"], "support": {"issues": "https://github.com/ChristianRiesen/base32/issues", "source": "https://github.com/ChristianRiesen/base32/tree/1.6.0"}, "time": "2021-02-26T10:19:33+00:00"}, {"name": "dasprid/enum", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.6"}, "time": "2024-08-09T14:30:48+00:00"}, {"name": "doctrine/annotations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/901c2ee5d26eb64ff43c47976e114bf00843acf7", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7", "shasum": ""}, "require": {"doctrine/lexer": "^2 || ^3", "ext-tokenizer": "*", "php": "^7.2 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.2"}, "time": "2024-09-05T10:17:24+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "ec16c82f20be1a7224e65ac67144a29199f87959"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/ec16c82f20be1a7224e65ac67144a29199f87959", "reference": "ec16c82f20be1a7224e65ac67144a29199f87959", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "9.6.22", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.2", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.9.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2025-01-16T08:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.20.0", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "068d9fcf912c88a0471d101d95a2caa87c50aee7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/068d9fcf912c88a0471d101d95a2caa87c50aee7", "reference": "068d9fcf912c88a0471d101d95a2caa87c50aee7", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.20.0"}, "time": "2024-09-05T10:18:12+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "time": "2025-04-09T20:32:01+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "lolli42/finediff", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/lolli42/FineDiff.git", "reference": "807deaf7aa119cf47b58e90ba7bf37c8c8264c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lolli42/FineDiff/zipball/807deaf7aa119cf47b58e90ba7bf37c8c8264c7a", "reference": "807deaf7aa119cf47b58e90ba7bf37c8c8264c7a", "shasum": ""}, "require": {"php": ">=7.2.0", "symfony/polyfill-mbstring": "^1.23"}, "replace": {"cogpowered/finediff": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "phpstan/phpstan": "^1.9.14", "phpunit/phpunit": "^8.5.33 || ^9.6.11 || ^10.3.2"}, "type": "library", "autoload": {"psr-4": {"cogpowered\\FineDiff\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "maintainer"}], "description": "PHP implementation of a Fine granularity Diff engine", "homepage": "https://github.com/lolli42/FineDiff", "keywords": ["diff", "finediff", "opcode", "string", "text"], "support": {"issues": "https://github.com/lolli42/FineDiff/issues", "source": "https://github.com/lolli42/FineDiff/tree/1.0.4"}, "time": "2024-07-09T14:52:10+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "time": "2025-04-13T19:20:35+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.33.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "82a311fd3690fb2bf7b64d5c98f912b3dd746140"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/82a311fd3690fb2bf7b64d5c98f912b3dd746140", "reference": "82a311fd3690fb2bf7b64d5c98f912b3dd746140", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.33.0"}, "time": "2024-10-13T11:25:22+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "symfony/cache", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-08T08:21:20+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/5d68a57d66910405e5c0b63d6f0af941e66fc868", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T15:25:07+00:00"}, {"name": "symfony/clock", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b2bf55c4dd115003309eafa87ee7df9ed3dde81b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b2bf55c4dd115003309eafa87ee7df9ed3dde81b", "reference": "b2bf55c4dd115003309eafa87ee7df9ed3dde81b", "shasum": ""}, "require": {"php": ">=8.1", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/config", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "af5917a3b1571f54689e56677a3f06440d2fe4c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/af5917a3b1571f54689e56677a3f06440d2fe4c7", "reference": "af5917a3b1571f54689e56677a3f06440d2fe4c7", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<5.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-14T06:00:01+00:00"}, {"name": "symfony/console", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-07T07:05:04+00:00"}, {"name": "symfony/dependency-injection", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "8cb11f833d1f5bfbb2df97dfc23c92b4d42c18d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/8cb11f833d1f5bfbb2df97dfc23c92b4d42c18d9", "reference": "8cb11f833d1f5bfbb2df97dfc23c92b4d42c18d9", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.1", "symfony/finder": "<5.4", "symfony/proxy-manager-bridge": "<6.3", "symfony/yaml": "<5.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.1|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-17T07:35:26+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/doctrine-messenger", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-messenger.git", "reference": "5437d2cde244a4a4b657bc7f3aceb1ce6c56ea7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-messenger/zipball/5437d2cde244a4a4b657bc7f3aceb1ce6c56ea7f", "reference": "5437d2cde244a4a4b657bc7f3aceb1ce6c56ea7f", "shasum": ""}, "require": {"doctrine/dbal": "^2.13|^3|^4", "php": ">=8.1", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-messenger/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-06T15:50:19+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/expression-language", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "3524904fb026356a5230cd197f9a4e6a61e0e7df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/3524904fb026356a5230cd197f9a4e6a61e0e7df", "reference": "3524904fb026356a5230cd197f9a4e6a61e0e7df", "shasum": ""}, "require": {"php": ">=8.1", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-09T08:40:40+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:07:50+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T13:51:37+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae", "reference": "6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-11T15:36:20+00:00"}, {"name": "symfony/mailer", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/ada2809ccd4ec27aba9fc344e3efdaec624c6438", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-26T23:47:35+00:00"}, {"name": "symfony/messenger", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "f99fbe6a4727ea504eb9ecc9f76e1ed61d2d6f33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/f99fbe6a4727ea504eb9ecc9f76e1ed61d2d6f33", "reference": "f99fbe6a4727ea504eb9ecc9f76e1ed61d2d6f33", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/clock": "^6.3|^7.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.3", "symfony/event-dispatcher": "<5.4", "symfony/event-dispatcher-contracts": "<2.5", "symfony/framework-bundle": "<5.4", "symfony/http-kernel": "<5.4", "symfony/serializer": "<5.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^6.3|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/validator": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:27:38+00:00"}, {"name": "symfony/mime", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/fec8aa5231f3904754955fad33c2db50594d22d1", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:27:38+00:00"}, {"name": "symfony/options-resolver", "version": "v6.4.16", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "368128ad168f20e22c32159b9f761e456cec0c78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/368128ad168f20e22c32159b9f761e456cec0c78", "reference": "368128ad168f20e22c32159b9f761e456cec0c78", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v6.4.16"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-20T10:57:02+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/property-access", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "80e0378f2f058b60d87dedc3c760caec882e992c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/80e0378f2f058b60d87dedc3c760caec882e992c", "reference": "80e0378f2f058b60d87dedc3c760caec882e992c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/property-info": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/cache": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-16T14:42:05+00:00"}, {"name": "symfony/property-info", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "94d18e5cc11a37fd92856d38b61d9cdf72536a1e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/94d18e5cc11a37fd92856d38b61d9cdf72536a1e", "reference": "94d18e5cc11a37fd92856d38b61d9cdf72536a1e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/cache": "<5.4", "symfony/dependency-injection": "<5.4|>=6.0,<6.4", "symfony/serializer": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/serializer": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-21T10:52:27+00:00"}, {"name": "symfony/rate-limiter", "version": "v6.4.15", "source": {"type": "git", "url": "https://github.com/symfony/rate-limiter.git", "reference": "e250d82fc17b277b97cbce94efef5414aff29bf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/rate-limiter/zipball/e250d82fc17b277b97cbce94efef5414aff29bf9", "reference": "e250d82fc17b277b97cbce94efef5414aff29bf9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/options-resolver": "^5.4|^6.0|^7.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/lock": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\RateLimiter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a Token Bucket implementation to rate limit input and output in your application", "homepage": "https://symfony.com", "keywords": ["limiter", "rate-limiter"], "support": {"source": "https://github.com/symfony/rate-limiter/tree/v6.4.15"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-09T07:19:24+00:00"}, {"name": "symfony/routing", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/1f5234e8457164a3a0038a4c0a4ba27876a9c670", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T16:08:38+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/string", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "73e2c6966a5aef1d4892873ed5322245295370c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/73e2c6966a5aef1d4892873ed5322245295370c6", "reference": "73e2c6966a5aef1d4892873ed5322245295370c6", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-18T15:23:29+00:00"}, {"name": "symfony/uid", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "18eb207f0436a993fffbdd811b5b8fa35fa5e007"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/18eb207f0436a993fffbdd811b5b8fa35fa5e007", "reference": "18eb207f0436a993fffbdd811b5b8fa35fa5e007", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/var-exporter", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/f28cf841f5654955c9f88ceaf4b9dc29571988a9", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-14T13:00:13+00:00"}, {"name": "symfony/yaml", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/f01987f45676778b474468aa266fe2eda1f2bc7e", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T09:48:44+00:00"}, {"name": "typo3/class-alias-loader", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/TYPO3/class-alias-loader.git", "reference": "cf2aebabe1886474da7194e1531900039263b3e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/class-alias-loader/zipball/cf2aebabe1886474da7194e1531900039263b3e0", "reference": "cf2aebabe1886474da7194e1531900039263b3e0", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=7.1"}, "replace": {"helhum/class-alias-loader": "*"}, "require-dev": {"composer/composer": "^1.1@dev || ^2.0@dev", "mikey179/vfsstream": "~1.4.0@dev", "phpunit/phpunit": ">4.8 <9"}, "type": "composer-plugin", "extra": {"class": "TYPO3\\ClassAliasLoader\\Plugin", "branch-alias": {"dev-main": "1.1.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\ClassAliasLoader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Amends the composer class loader to support class aliases to provide backwards compatibility for packages", "homepage": "http://github.com/TYPO3/class-alias-loader", "keywords": ["alias", "autoloader", "classloader", "composer"], "support": {"issues": "https://github.com/TYPO3/class-alias-loader/issues", "source": "https://github.com/TYPO3/class-alias-loader/tree/v1.2.0"}, "time": "2024-10-11T08:11:39+00:00"}, {"name": "typo3/cms-backend", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/backend.git", "reference": "f3a60987d70a3f715111e3aac3db0f14d21e8ada"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/backend/zipball/f3a60987d70a3f715111e3aac3db0f14d21e8ada", "reference": "f3a60987d70a3f715111e3aac3db0f14d21e8ada", "shasum": ""}, "require": {"ext-intl": "*", "psr/event-dispatcher": "^1.0", "typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "replace": {"typo3/cms-about": "self.version", "typo3/cms-context-help": "self.version", "typo3/cms-cshmanual": "self.version", "typo3/cms-func-wizards": "self.version", "typo3/cms-recordlist": "self.version", "typo3/cms-wizard-crpages": "self.version", "typo3/cms-wizard-sortpages": "self.version"}, "suggest": {"typo3/cms-install": "To generate url to install tool in environment toolbar"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Backend\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "backend"}, "branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/class-alias-loader": {"class-alias-maps": ["Migrations/Code/ClassAliasMap.php"]}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Backend\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS backend", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-belog", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/belog.git", "reference": "3cd5c524c8102a289214f40283b77d61517a4c30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/belog/zipball/3cd5c524c8102a289214f40283b77d61517a4c30", "reference": "3cd5c524c8102a289214f40283b77d61517a4c30", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "belog"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Belog\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Log - View logs from the sys_log table in the TYPO3 backend modules System>Log", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-beuser", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/beuser.git", "reference": "677536eafb38f12a6d72d8ba116c7abf021cb4b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/beuser/zipball/677536eafb38f12a6d72d8ba116c7abf021cb4b4", "reference": "677536eafb38f12a6d72d8ba116c7abf021cb4b4", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "beuser"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Beuser\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Backend User - TYPO3 backend module System>Backend Users for managing backend users and groups.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-cli", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/TYPO3/cms-cli.git", "reference": "e7ad171e5abf790db17296d33268e1a620452558"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/cms-cli/zipball/e7ad171e5abf790db17296d33268e1a620452558", "reference": "e7ad171e5abf790db17296d33268e1a620452558", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "conflict": {"phpstan/phpdoc-parser": "<1.0 || >=2.0"}, "bin": ["typo3"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "TYPO3 command line binary", "homepage": "https://typo3.org", "support": {"issues": "https://github.com/TYPO3/cms-cli/issues", "source": "https://github.com/TYPO3/cms-cli/tree/3.1.2"}, "time": "2024-11-13T12:02:24+00:00"}, {"name": "typo3/cms-composer-installers", "version": "v5.0.1", "source": {"type": "git", "url": "https://github.com/TYPO3/CmsComposerInstallers.git", "reference": "444a228d3ae4320d7ba0b769cfab008b0c09443c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/CmsComposerInstallers/zipball/444a228d3ae4320d7ba0b769cfab008b0c09443c", "reference": "444a228d3ae4320d7ba0b769cfab008b0c09443c", "shasum": ""}, "require": {"composer-plugin-api": "^2.1.0", "php": "^8.1"}, "replace": {"lw/typo3cms-installers": "*", "netresearch/composer-installers": "*"}, "require-dev": {"composer/composer": "^2.1", "friendsofphp/php-cs-fixer": "^2.18", "overtrue/phplint": "^2.0", "phpunit/phpunit": "^8.5"}, "type": "composer-plugin", "extra": {"class": "TYPO3\\CMS\\Composer\\Installer\\Plugin", "branch-alias": {"dev-main": "5.0.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 CMS Core Team", "homepage": "https://forge.typo3.org/projects/typo3cms-core", "role": "Developer"}, {"name": "The TYPO3 Community", "homepage": "https://typo3.org/community/", "role": "Contributor"}], "description": "TYPO3 CMS Installers", "homepage": "https://github.com/TYPO3/CmsComposerInstallers", "keywords": ["cms", "core", "extension", "installer", "typo3"], "support": {"general": "https://typo3.org/support/", "issues": "https://github.com/TYPO3/CmsComposerInstallers/issues", "source": "https://github.com/TYPO3/CmsComposerInstallers/tree/v5.0.1"}, "time": "2024-08-13T14:58:06+00:00"}, {"name": "typo3/cms-core", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/core.git", "reference": "def57d52a7788e04af5e7c2bd4057079eed36ee2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/core/zipball/def57d52a7788e04af5e7c2bd4057079eed36ee2", "reference": "def57d52a7788e04af5e7c2bd4057079eed36ee2", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^2.0.7 || ^3.0.1", "christian-riesen/base32": "^1.6", "composer-runtime-api": "^2.1", "doctrine/annotations": "^1.13.3 || ^2.0", "doctrine/dbal": "^3.9", "doctrine/event-manager": "^2.0", "doctrine/lexer": "^2.0 || ^3.0", "egulias/email-validator": "^4.0", "enshrined/svg-sanitize": "^0.18.0 || ^0.20.0", "ext-dom": "*", "ext-intl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-tokenizer": "*", "ext-xml": "*", "firebase/php-jwt": "^6.4.0", "guzzlehttp/guzzle": "^7.7.0", "guzzlehttp/psr7": "^2.5.0", "lolli42/finediff": "^1.0.2", "masterminds/html5": "^2.7.6", "php": "^8.1", "psr/container": "^2.0", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "psr/log": "^2.0 || ^3.0", "symfony/config": "^6.4 || ^7.0", "symfony/console": "^6.4 || ^7.0", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/doctrine-messenger": "^6.4 || ^7.0", "symfony/event-dispatcher-contracts": "^3.1", "symfony/expression-language": "^6.4 || ^7.0", "symfony/filesystem": "^6.4 || ^7.0", "symfony/finder": "^6.4 || ^7.0", "symfony/http-foundation": "^6.4 || ^7.0", "symfony/mailer": "^6.4 || ^7.0", "symfony/messenger": "^6.4 || ^7.0", "symfony/mime": "^6.4 || ^7.0", "symfony/options-resolver": "^6.4 || ^7.0", "symfony/polyfill-php83": "^1.31", "symfony/rate-limiter": "^6.4 || ^7.0", "symfony/routing": "^6.4 || ^7.0", "symfony/uid": "^6.4 || ^7.0", "symfony/yaml": "^6.4 || ^7.0", "typo3/class-alias-loader": "^1.1.4", "typo3/cms-cli": "^3.1", "typo3/cms-composer-installers": "^5.0", "typo3/html-sanitizer": "^2.1.4", "typo3fluid/fluid": "^2.15.0"}, "conflict": {"hoa/core": "*", "typo3/cms": "*"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "replace": {"typo3/cms-lang": "self.version", "typo3/cms-saltedpasswords": "self.version", "typo3/cms-sv": "self.version"}, "suggest": {"ext-apcu": "Needed when non-default APCU based cache backends are used", "ext-fileinfo": "Used for proper file type detection in the file abstraction layer", "ext-gd": "GDlib/Freetype is required for building images with text (GIFBUILDER) and can also be used to scale images", "ext-mysqli": "", "ext-openssl": "OpenSSL is required for sending SMTP mails over an encrypted channel endpoint", "ext-zip": "", "ext-zlib": "TYPO3 uses zlib for amongst others output compression and un/packing t3x extension files"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Core\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "core"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"files": ["Resources/PHP/GlobalDebugFunctions.php"], "psr-4": {"TYPO3\\CMS\\Core\\": "Classes/"}, "classmap": ["Resources/PHP/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Core", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-dashboard", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/dashboard.git", "reference": "7e3cad15cf1602c874edd2ee80e75d28bb2f4183"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/dashboard/zipball/7e3cad15cf1602c874edd2ee80e75d28bb2f4183", "reference": "7e3cad15cf1602c874edd2ee80e75d28bb2f4183", "shasum": ""}, "require": {"typo3/cms-backend": "12.4.33", "typo3/cms-core": "12.4.33", "typo3/cms-extbase": "12.4.33", "typo3/cms-fluid": "12.4.33", "typo3/cms-frontend": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"serviceProvider": "TYPO3\\CMS\\Dashboard\\ServiceProvider", "partOfFactoryDefault": true}, "extension-key": "dashboard"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Dashboard\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Dashboard - TYPO3 backend module used to configure and create backend widgets.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-dashboard/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-extbase", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/extbase.git", "reference": "90f23dd982e1a5402a4ddc6b67a6052e9b7f4bc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/extbase/zipball/90f23dd982e1a5402a4ddc6b67a6052e9b7f4bc4", "reference": "90f23dd982e1a5402a4ddc6b67a6052e9b7f4bc4", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5 || ^2.0", "phpdocumentor/reflection-docblock": "^5.2", "phpdocumentor/type-resolver": "^1.7.1", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/property-access": "^6.4 || ^7.0", "symfony/property-info": "^6.4 || ^7.0", "typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-scheduler": "Additional scheduler tasks"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Extbase\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "extbase"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Extbase\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Extbase - Extension framework to create TYPO3 frontend plugins and TYPO3 backend modules.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-extensionmanager", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/extensionmanager.git", "reference": "97ca5395ed8c95038e7221405ea629518a700500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/extensionmanager/zipball/97ca5395ed8c95038e7221405ea629518a700500", "reference": "97ca5395ed8c95038e7221405ea629518a700500", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "extensionmanager"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Extensionmanager\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Extension Manager - Backend module (Admin Tools>Extensions) for viewing and managing extensions.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-felogin", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/felogin.git", "reference": "d00549b2a55e341fa8d37d9d3fe6d62b76364f89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/felogin/zipball/d00549b2a55e341fa8d37d9d3fe6d62b76364f89", "reference": "d00549b2a55e341fa8d37d9d3fe6d62b76364f89", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "felogin"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\FrontendLogin\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Frontend Login - A template-based plugin to log in website users in the TYPO3 frontend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-felogin/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-filelist", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/filelist.git", "reference": "450d68907460f75180fc3b216aa136ddfa1dbae8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/filelist/zipball/450d68907460f75180fc3b216aa136ddfa1dbae8", "reference": "450d68907460f75180fc3b216aa136ddfa1dbae8", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "filelist"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Filelist\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Filelist - TYPO3 backend module (File>Filelist) used for managing files.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-filemetadata", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/filemetadata.git", "reference": "bc6768c98b21f5ba30817da133b4f56c29724f32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/filemetadata/zipball/bc6768c98b21f5ba30817da133b4f56c29724f32", "reference": "bc6768c98b21f5ba30817da133b4f56c29724f32", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"extension-key": "filemetadata"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS File Metadata - Adds additional metadata to file management.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-fluid", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/fluid.git", "reference": "86e1a453d706722a0d74346300379cbeba8b47da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/fluid/zipball/86e1a453d706722a0d74346300379cbeba8b47da", "reference": "86e1a453d706722a0d74346300379cbeba8b47da", "shasum": ""}, "require": {"symfony/dependency-injection": "^6.4 || ^7.0", "typo3/cms-core": "12.4.33", "typo3/cms-extbase": "12.4.33", "typo3fluid/fluid": "^2.15.0"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Fluid\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "fluid"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Fluid\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Fluid Integration - Integration of the Fluid templating engine into TYPO3.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/other/typo3/view-helper-reference/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-fluid-styled-content", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/fluid_styled_content.git", "reference": "b80fa691080e4735da6d01fc8c8e25c88884defe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/fluid_styled_content/zipball/b80fa691080e4735da6d01fc8c8e25c88884defe", "reference": "b80fa691080e4735da6d01fc8c8e25c88884defe", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33", "typo3/cms-fluid": "12.4.33", "typo3/cms-frontend": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "fluid_styled_content"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\FluidStyledContent\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Fluid Styled Content - Fluid templates for TYPO3 content elements.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-fluid-styled-content/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-form", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/form.git", "reference": "6f6e521789ab2bfaf96e67e63e2c729000880158"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/form/zipball/6f6e521789ab2bfaf96e67e63e2c729000880158", "reference": "6f6e521789ab2bfaf96e67e63e2c729000880158", "shasum": ""}, "require": {"psr/http-message": "^1.1 || ^2.0", "symfony/expression-language": "^6.4 || ^7.0", "typo3/cms-core": "12.4.33", "typo3/cms-frontend": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-filelist": "Listing of files in the directory", "typo3/cms-impexp": "Import and Export of records from TYPO3 in a custom serialized format (.T3D) for data exchange with other TYPO3 systems.", "typo3/cms-lowlevel": "To display the YAML configuration in the configuration module"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "form"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Form\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Form - Flexible TYPO3 frontend form framework that comes with a backend editor interface.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-form/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-frontend", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/frontend.git", "reference": "80d9023f616403a1512597cf12af61217648e797"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/frontend/zipball/80d9023f616403a1512597cf12af61217648e797", "reference": "80d9023f616403a1512597cf12af61217648e797", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-adminpanel": "Provides additional information and functionality for backend users in the frontend."}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Frontend\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "frontend"}, "branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/class-alias-loader": {"class-alias-maps": ["Migrations/Code/ClassAliasMap.php"]}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Frontend\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Frontend", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-impexp", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/impexp.git", "reference": "b59201ba72a6c2c2296c9b0c336e962c27254df9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/impexp/zipball/b59201ba72a6c2c2296c9b0c336e962c27254df9", "reference": "b59201ba72a6c2c2296c9b0c336e962c27254df9", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "impexp"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Impexp\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Import/Export - Tool for importing and exporting records using XML or the custom T3D format.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-impexp/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-info", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/info.git", "reference": "457311ecb5f96f595b58db1e65698491bd8204b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/info/zipball/457311ecb5f96f595b58db1e65698491bd8204b1", "reference": "457311ecb5f96f595b58db1e65698491bd8204b1", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "replace": {"typo3/cms-info-pagetsconfig": "self.version"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "info"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Info\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Info - TYPO3 backend module for displaying information, such as a pagetree overview and localization information.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-install", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/install.git", "reference": "c0e9299813b0b67b11cf0966fa94ee1519c32a3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/install/zipball/c0e9299813b0b67b11cf0966fa94ee1519c32a3e", "reference": "c0e9299813b0b67b11cf0966fa94ee1519c32a3e", "shasum": ""}, "require": {"doctrine/dbal": "^3.9", "guzzlehttp/promises": "^1.5.2 || ^2.0", "nikic/php-parser": "^4.15.4", "symfony/finder": "^6.4 || ^7.0", "symfony/http-foundation": "^6.4 || ^7.0", "typo3/cms-core": "12.4.33", "typo3/cms-extbase": "12.4.33", "typo3/cms-fluid": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"protected": true, "serviceProvider": "TYPO3\\CMS\\Install\\ServiceProvider", "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "install"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Install\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Install Tool - The Install Tool is used for installation, upgrade, system administration and setup tasks.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-reactions", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/reactions.git", "reference": "c5f685750d2e2f1c7f5fa64c91d319bc2a105c5f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/reactions/zipball/c5f685750d2e2f1c7f5fa64c91d319bc2a105c5f", "reference": "c5f685750d2e2f1c7f5fa64c91d319bc2a105c5f", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-lowlevel": "To display registered reactions in the configuration module"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"extension-key": "reactions"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Reactions\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Reactions - Handle incoming Webhooks for TYPO3", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-reactions/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-redirects", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/redirects.git", "reference": "824dd304a43a4faf784a9d35d5c616f6ee2bdbe3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/redirects/zipball/824dd304a43a4faf784a9d35d5c616f6ee2bdbe3", "reference": "824dd304a43a4faf784a9d35d5c616f6ee2bdbe3", "shasum": ""}, "require": {"doctrine/dbal": "^3.9", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^2.0 || ^3.0", "symfony/console": "^6.4 || ^7.0", "typo3/cms-backend": "12.4.33", "typo3/cms-core": "12.4.33", "typo3fluid/fluid": "^2.15.0"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-reports": "Get reports of redirects", "typo3/cms-scheduler": "Execute commands to update redirect status"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "redirects"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Redirects\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Redirects - Create manual redirects, list existing redirects and automatically createredirects on slug changes.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-redirects/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-rte-ckeditor", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/rte_ckeditor.git", "reference": "17944a3a4e7c5884962e20cd1191f05dfe19c08e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/rte_ckeditor/zipball/17944a3a4e7c5884962e20cd1191f05dfe19c08e", "reference": "17944a3a4e7c5884962e20cd1191f05dfe19c08e", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-setup": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "rte_ckeditor"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\RteCKEditor\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS RTE CKEditor - Integration of CKEditor as a Rich Text Editor for the TYPO3 backend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-rte-ckeditor/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-seo", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/seo.git", "reference": "c4dead13f0c41f47b67ad95e7f40cf4c3a0b4664"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/seo/zipball/c4dead13f0c41f47b67ad95e7f40cf4c3a0b4664", "reference": "c4dead13f0c41f47b67ad95e7f40cf4c3a0b4664", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33", "typo3/cms-extbase": "12.4.33", "typo3/cms-frontend": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-dashboard": "TYPO3 users can add widgets that can help to optimise their website for search engines"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "seo"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Seo\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS SEO - SEO features including specific fields for SEO purposes, rendering of HTML meta tags and sitemaps.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-seo/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-setup", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/setup.git", "reference": "942735ad645799ac2bf1aeec9db771f0783ef54d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/setup/zipball/942735ad645799ac2bf1aeec9db771f0783ef54d", "reference": "942735ad645799ac2bf1aeec9db771f0783ef54d", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "setup"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Setup\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Setup - Allows users to edit a limited set of options for their user profile, including preferred language, their name and email address.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-sys-note", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/sys_note.git", "reference": "bb45b39b561d3364e9c7f7e2441e08a325cdce85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/sys_note/zipball/bb45b39b561d3364e9c7f7e2441e08a325cdce85", "reference": "bb45b39b561d3364e9c7f7e2441e08a325cdce85", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "sys_note"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\SysNote\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS System Notes - Records with messages which can be placed on any page and contain instructions or other information related to a page or section.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-t3editor", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/t3editor.git", "reference": "4d84db0b0b4224b8b8ec832f21f1d9ce781c87fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/t3editor/zipball/4d84db0b0b4224b8b8ec832f21f1d9ce781c87fe", "reference": "4d84db0b0b4224b8b8ec832f21f1d9ce781c87fe", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "t3editor"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\T3editor\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS T3Editor - JavaScript-driven editor with syntax highlighting and code completion. Based on CodeMirror.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-t3editor/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-tstemplate", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/tstemplate.git", "reference": "2770f3703a31e53dc93647207b85135ba0b29c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/tstemplate/zipball/2770f3703a31e53dc93647207b85135ba0b29c5e", "reference": "2770f3703a31e53dc93647207b85135ba0b29c5e", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "tstemplate"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Tstemplate\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS TypoScript - TYPO3 backend module for the management of TypoScript records for the CMS frontend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-viewpage", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/viewpage.git", "reference": "790c23f142d41f7eea3e3c09a7136ef07f23e130"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/viewpage/zipball/790c23f142d41f7eea3e3c09a7136ef07f23e130", "reference": "790c23f142d41f7eea3e3c09a7136ef07f23e130", "shasum": ""}, "require": {"typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "viewpage"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Viewpage\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Viewpage - Use the (Web>View) backend module to view a frontend page inside the TYPO3 backend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/cms-webhooks", "version": "v12.4.33", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/webhooks.git", "reference": "40710cf20c10eb56b3671c076a34fe2cb1414c44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/webhooks/zipball/40710cf20c10eb56b3671c076a34fe2cb1414c44", "reference": "40710cf20c10eb56b3671c076a34fe2cb1414c44", "shasum": ""}, "require": {"symfony/uid": "^6.4 || ^7.0", "typo3/cms-core": "12.4.33"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-lowlevel": "To display registered webhooks in the configuration module"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"extension-key": "webhooks"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Webhooks\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Webhooks - Handle outgoing Webhooks for TYPO3", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-webhooks/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2025-06-10T11:45:13+00:00"}, {"name": "typo3/html-sanitizer", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/TYPO3/html-sanitizer.git", "reference": "c672a2e02925de8eed0dcaeb3a3c90d3642049a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/html-sanitizer/zipball/c672a2e02925de8eed0dcaeb3a3c90d3642049a0", "reference": "c672a2e02925de8eed0dcaeb3a3c90d3642049a0", "shasum": ""}, "require": {"ext-dom": "*", "masterminds/html5": "^2.7.6", "php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\HtmlSanitizer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTML sanitizer aiming to provide XSS-safe markup based on explicitly allowed tags, attributes and values.", "support": {"issues": "https://github.com/TYPO3/html-sanitizer/issues", "source": "https://github.com/TYPO3/html-sanitizer/tree/v2.2.0"}, "time": "2024-07-12T15:52:25+00:00"}, {"name": "typo3fluid/fluid", "version": "2.15.0", "source": {"type": "git", "url": "https://github.com/TYPO3/Fluid.git", "reference": "0a8ebdb9bab1510380f18bef6395fbb4754c01b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/Fluid/zipball/0a8ebdb9bab1510380f18bef6395fbb4754c01b7", "reference": "0a8ebdb9bab1510380f18bef6395fbb4754c01b7", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.1"}, "require-dev": {"ext-json": "*", "ext-simplexml": "*", "friendsofphp/php-cs-fixer": "^3.59.3", "phpstan/phpstan": "^1.10.14", "phpstan/phpstan-phpunit": "^1.3.11", "phpunit/phpunit": "^10.2.6"}, "suggest": {"ext-json": "PHP JSON is needed when using JSONVariableProvider: A relatively rare use case", "ext-simplexml": "SimpleXML is required for the XSD schema generator"}, "bin": ["bin/fluid"], "type": "library", "autoload": {"psr-4": {"TYPO3Fluid\\Fluid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "description": "The TYPO3 Fluid template rendering engine", "homepage": "https://github.com/TYPO3/Fluid", "support": {"docs": "https://docs.typo3.org/other/typo3fluid/fluid/main/en-us/", "issues": "https://github.com/TYPO3/Fluid/issues", "source": "https://github.com/TYPO3/Fluid"}, "time": "2024-08-30T21:24:26+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "b13/make", "version": "0.1.8", "source": {"type": "git", "url": "https://github.com/b13/make.git", "reference": "d7f302a0211aeab90ac15825bf49fdf914d79ed9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/b13/make/zipball/d7f302a0211aeab90ac15825bf49fdf914d79ed9", "reference": "d7f302a0211aeab90ac15825bf49fdf914d79ed9", "shasum": ""}, "require": {"typo3/cms-core": "^10.0 || ^11.0 || ^12.0 || ^13.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "typo3/cms-core": "^11.5", "typo3/coding-standards": "^0.5", "typo3/tailor": "^1.4", "typo3/testing-framework": "^7.0"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"web-dir": ".Build/Web", "extension-key": "make", "cms-package-dir": "{$vendor-dir}/typo3/cms"}}, "autoload": {"psr-4": {"B13\\Make\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Kickstarter CLI tool for various TYPO3 functionalities", "homepage": "https://b13.com", "keywords": ["cli", "extensions", "kickstarter", "typo3"], "support": {"issues": "https://github.com/b13/make/issues", "source": "https://github.com/b13/make/tree/0.1.8"}, "time": "2024-08-05T08:25:45+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"bgs/landing-pages": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "platform-overrides": {"php": "8.1.1"}, "plugin-api-version": "2.6.0"}