# Production Fix for DynamicArgumentsMiddleware Error

## Problem
The error occurs in production when the `DynamicArgumentsMiddleware` tries to call `getOriginalPath()` on a null `$virtualRouteContext` object.

**Error**: `Call to a member function getOriginalPath() on null`
**File**: `Classes/Middleware/DynamicArgumentsMiddleware.php` line 36
**URL**: `http://www.dertour.bg/samoletni-pochivki`

## Root Cause
The URL `/samoletni-pochivki` has only one path segment, which doesn't match the virtual route pattern (which requires at least 2 segments: landing page + route slug). However, the `DynamicArgumentsMiddleware` is still being called and trying to access the virtual route context that was never set.

## Immediate Fix Applied

I've updated the `DynamicArgumentsMiddleware` to be more robust:

1. **Added proper type checking** - Ensures the context is a `VirtualRouteContext` instance
2. **Added null checking** - Checks if `getOriginalPath()` returns null
3. **Added logging** - For debugging in production
4. **Improved error handling** - Graceful fallback for all edge cases

## Files Modified

### 1. `Classes/Middleware/DynamicArgumentsMiddleware.php`
- Added `VirtualRouteContext` import
- Added proper instanceof checking
- Added null checking for `getOriginalPath()`
- Added logging for debugging

### 2. `Classes/Middleware/VirtualRouteHandler.php`
- Added logging to track route detection

## Testing the Fix

### 1. Test Normal Pages
Visit pages that are NOT virtual routes:
- `/samoletni-pochivki` (the failing URL)
- `/about`
- `/contact`

These should work normally without errors.

### 2. Test Virtual Routes
Visit actual virtual routes (if they exist):
- `/flights/ber-sof` (example)
- Any URL with landing page + route slug pattern

### 3. Check Logs
Enable logging to see what's happening:

```php
// In AdditionalConfiguration.php or ext_localconf.php
$GLOBALS['TYPO3_CONF_VARS']['LOG']['Bgs']['LandingPages'] = [
    'writerConfiguration' => [
        \TYPO3\CMS\Core\Log\LogLevel::DEBUG => [
            \TYPO3\CMS\Core\Log\Writer\FileWriter::class => [
                'logFile' => 'typo3temp/logs/landing-pages.log'
            ]
        ]
    ]
];
```

## Deployment Instructions

1. **Upload the modified files**:
   - `Classes/Middleware/DynamicArgumentsMiddleware.php`
   - `Classes/Middleware/VirtualRouteHandler.php`

2. **Clear all caches**:
   ```bash
   # Via CLI
   typo3cms cache:flush
   
   # Or via Install Tool
   # Go to Admin Tools > Maintenance > Flush TYPO3 and PHP Caches
   ```

3. **Test the previously failing URL**:
   - Visit `http://www.dertour.bg/samoletni-pochivki`
   - Should work without errors

## Long-term Considerations

### 1. Virtual Route Configuration
If `/samoletni-pochivki` should be a virtual route, you need to:
- Create a landing page with slug `/samoletni-pochivki`
- Create flight routes as sub-pages
- URLs would then be `/samoletni-pochivki/ber-sof` format

### 2. URL Structure Review
Current virtual route detection expects:
- Landing page path: `/flights`
- Route slug: `ber-sof`
- Full URL: `/flights/ber-sof`

If you want single-segment virtual routes like `/samoletni-pochivki`, the detection logic needs modification.

### 3. Monitoring
Monitor the logs to see:
- Which URLs are being processed
- Whether virtual route detection is working correctly
- Any other potential issues

## Rollback Plan

If issues persist, you can temporarily disable the middleware by commenting out the configuration in `Configuration/RequestMiddlewares.php`:

```php
return [
    'frontend' => [
        // Temporarily disable if needed
        // 'landing-pages/dynamic-arguments' => [
        //     'target' => \Bgs\LandingPages\Middleware\DynamicArgumentsMiddleware::class,
        //     'after' => ['typo3/cms-frontend/page-argument-validator'],
        //     'before' => ['typo3/cms-frontend/tsfe'],
        // ],
    ],
];
```

This will disable the dynamic arguments processing but keep virtual routes working.

## Contact

If the issue persists after applying this fix, please provide:
1. The exact error message
2. The URL that's failing
3. Log files from `typo3temp/logs/`
4. TYPO3 and PHP versions
