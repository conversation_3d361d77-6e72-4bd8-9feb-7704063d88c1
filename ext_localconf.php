<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Configure DestinationsMenu plugin as fallback for any remaining plugin content elements
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
        'Bgs.LandingPages',
        'DestinationsMenu',
        [
            \Bgs\LandingPages\Controller\DestinationsMenuController::class => 'list',
        ],
        // non-cacheable actions
        []
    );



    // Register page type icons
    $iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-template',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:landing-pages/Resources/Public/Icons/apps-pagetree-flight-template.svg']
    );
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-landing',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:landing-pages/Resources/Public/Icons/apps-pagetree-flight-landing.svg']
    );
    $iconRegistry->registerIcon(
        'content-destination-pairs-menu',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:landing-pages/Resources/Public/Icons/content-destination-pairs-menu.svg']
    );

    // Register page TSconfig for TYPO3 v11 compatibility
    // In v12+ this is loaded automatically from Configuration/page.tsconfig
    $versionInformation = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Information\Typo3Version::class);
    if ($versionInformation->getMajorVersion() < 12) {
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPageTSConfig(
            '@import "EXT:landing-pages/Configuration/page.tsconfig"'
        );
    }

    // Include TypoScript setup and constants
    // Temporarily disabled again due to production errors with data processor references
    // \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTypoScriptConstants(
    //     '@import "EXT:landing-pages/Configuration/TypoScript/constants.typoscript"'
    // );
    // \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTypoScriptSetup(
    //     '@import "EXT:landing-pages/Configuration/TypoScript/setup.typoscript"'
    // );

    // Register DataHandler hooks for automatic slug updates
    $GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['t3lib/class.t3lib_tcemain.php']['processDatamapClass'][] =
        \Bgs\LandingPages\Hooks\DataHandlerHook::class;

    // Register middlewares directly in ext_localconf.php to ensure they're loaded
    // Virtual Route Handler - Must run BEFORE PageResolver to catch virtual routes
    $GLOBALS['TYPO3_CONF_VARS']['HTTP']['middleware']['frontend']['landing-pages/virtual-route-handler'] = [
        'target' => \Bgs\LandingPages\Middleware\VirtualRouteHandler::class,
        'after' => [
            'typo3/cms-frontend/site',
            'typo3/cms-frontend/authentication',
            'typo3/cms-frontend/backend-user-authentication',
        ],
        'before' => [
            'typo3/cms-frontend/page-resolver',
        ],
    ];

    // Dynamic Arguments Middleware - Temporarily disabled to prevent interference
    // $GLOBALS['TYPO3_CONF_VARS']['HTTP']['middleware']['frontend']['landing-pages/dynamic-arguments'] = [
    //     'target' => \Bgs\LandingPages\Middleware\DynamicArgumentsMiddleware::class,
    //     'after' => [
    //         'typo3/cms-frontend/page-argument-validator',
    //     ],
    //     'before' => [
    //         'typo3/cms-frontend/tsfe',
    //     ],
    // ];

});
